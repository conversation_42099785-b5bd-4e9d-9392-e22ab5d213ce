import React, { useState } from 'react';

import {
    Analytics,
    BusinessCenter,
    CloudSync,
    Email as EmailIcon,
    Person as PersonIcon,
    Phone as PhoneIcon,
    Business as BusinessIcon,
    Security,
    SmartToy
} from '@mui/icons-material';
import {
    Alert,
    Box,
    Button,
    Card,
    CardContent,
    Checkbox,
    CircularProgress,
    FormControl,
    FormControlLabel,
    InputLabel,
    Link,
    MenuItem,
    Radio,
    RadioGroup,
    Select,
    TextField,
    Typography
} from '@mui/material';
import { useAuth } from '@shared/hooks/useAuth';
import { useNavigate } from '@tanstack/react-router';

const SignUpScreen: React.FC = () => {
    const navigate = useNavigate();
    const { register, loading, error } = useAuth();

    const [formData, setFormData] = useState({
        productType: '',
        businessType: 'doanh-nghiep',
        companyName: '',
        taxCode: '',
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        position: '',
        agreeToTerms: false
    });

    const [formErrors, setFormErrors] = useState<{
        [key: string]: string;
    }>({});

    const validateForm = () => {
        const errors: { [key: string]: string } = {};

        if (!formData.productType) {
            errors.productType = 'Vui lòng chọn sản phẩm';
        }
        if (!formData.companyName) {
            errors.companyName = 'Tên công ty là bắt buộc';
        }
        if (!formData.taxCode) {
            errors.taxCode = 'Mã số thuế là bắt buộc';
        }
        if (!formData.firstName) {
            errors.firstName = 'Họ và đệm là bắt buộc';
        }
        if (!formData.lastName) {
            errors.lastName = 'Tên là bắt buộc';
        }
        if (!formData.email) {
            errors.email = 'Email là bắt buộc';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            errors.email = 'Email không hợp lệ';
        }
        if (!formData.phone) {
            errors.phone = 'Số điện thoại là bắt buộc';
        }
        if (!formData.position) {
            errors.position = 'Vị trí công việc là bắt buộc';
        }
        if (!formData.agreeToTerms) {
            errors.agreeToTerms = 'Bạn phải đồng ý với các điều khoản';
        }

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        const result = await register(formData);

        if (result.success) {
            navigate({ to: '/dashboard' });
        }
    };

    const handleInputChange = (field: keyof typeof formData) => (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = field === 'agreeToTerms' ? e.target.checked : e.target.value;
        setFormData(prev => ({ ...prev, [field]: value }));

        // Clear field error when user starts typing
        if (formErrors[field]) {
            setFormErrors(prev => ({ ...prev, [field]: undefined }));
        }
    };

    const handleSelectChange = (field: keyof typeof formData) => (e: any) => {
        setFormData(prev => ({ ...prev, [field]: e.target.value }));
        if (formErrors[field]) {
            setFormErrors(prev => ({ ...prev, [field]: undefined }));
        }
    };

    return (
        <Box
            sx={{
                minHeight: '100vh',
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                backgroundImage: 'url(/assets/icons/images/windows-11-dark-mode-blue-stock-official-3840x2160-5630.jpg)',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                alignItems: 'center',
                justifyContent: 'center',
                p: 3,
                position: 'relative',
                '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.3)',
                    zIndex: 0
                }
            }}
        >
            {/* Combined Cards Container */}
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    boxShadow: 4,
                    borderRadius: 3,
                    overflow: 'hidden',
                    maxWidth: 1000,
                    width: '100%',
                    position: 'relative',
                    zIndex: 1
                }}
            >
                {/* Left Panel - Split Card */}
                <Card 
                    sx={{ 
                        width: { xs: '100%', md: 420 },
                        borderRadius: 0,
                        boxShadow: 'none',
                        overflow: 'hidden'
                    }}
                >
                    {/* Top Half of Left Card */}
                    <Box
                        sx={{
                            height: 300,
                            background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            p: 3,
                            position: 'relative',
                            overflow: 'hidden'
                        }}
                    >
                        {/* Background decorative elements */}
                        <Box
                            sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                opacity: 0.1,
                                background: `
                                    radial-gradient(circle at 20% 20%, rgba(255,255,255,0.3) 0%, transparent 50%),
                                    radial-gradient(circle at 80% 80%, rgba(255,255,255,0.2) 0%, transparent 50%)
                                `
                            }}
                        />

                        <Box sx={{ textAlign: 'center', zIndex: 1 }}>
                            <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                                MISA AMIS
                            </Typography>
                            <Typography variant="subtitle2" sx={{ opacity: 0.9, mb: 2 }}>
                                Hệ thống quản lý doanh nghiệp
                            </Typography>

                            {/* AI Robot Icon */}
                            <Box
                                sx={{
                                    width: 100,
                                    height: 100,
                                    borderRadius: '50%',
                                    backgroundColor: 'rgba(255,255,255,0.2)',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mx: 'auto',
                                    border: '2px solid rgba(255,255,255,0.3)'
                                }}
                            >
                                <SmartToy sx={{ fontSize: 50, color: 'white' }} />
                            </Box>
                        </Box>
                    </Box>

                    {/* Bottom Half of Left Card */}
                    <CardContent sx={{ p: 3, height: 300, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                        <Typography variant="h6" sx={{ fontWeight: 'bold', mb: 2, textAlign: 'center' }}>
                            Tính năng nổi bật
                        </Typography>
                        
                        {/* Feature Icons */}
                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mb: 3 }}>
                            <Box sx={{ textAlign: 'center' }}>
                                <BusinessCenter sx={{ fontSize: 28, mb: 1, color: '#1976d2' }} />
                                <Typography variant="caption" display="block">
                                    Quản lý tài liệu
                                </Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center' }}>
                                <Analytics sx={{ fontSize: 28, mb: 1, color: '#1976d2' }} />
                                <Typography variant="caption" display="block">
                                    Phân tích tài liệu
                                </Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center' }}>
                                <Security sx={{ fontSize: 28, mb: 1, color: '#1976d2' }} />
                                <Typography variant="caption" display="block">
                                    OCR
                                </Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center' }}>
                                <CloudSync sx={{ fontSize: 28, mb: 1, color: '#1976d2' }} />
                                <Typography variant="caption" display="block">
                                    Đồng bộ dữ liệu
                                </Typography>
                            </Box>
                        </Box>

                        <Typography variant="body2" color="text.secondary" textAlign="center">
                            Đăng ký ngay để trải nghiệm các tính năng mạnh mẽ
                        </Typography>
                    </CardContent>
                </Card>

                {/* Right Panel - Sign Up Form */}
                <Card sx={{ width: { xs: '100%', md: 580 }, borderRadius: 0, boxShadow: 'none', maxHeight: 600, overflow: 'auto' }}>
                    <CardContent sx={{ p: 4 }}>
                        <Box sx={{ textAlign: 'center', mb: 3 }}>
                            <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                                Đăng ký tài khoản dùng thử MISA AMIS
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                                Bạn đã sử dụng MISA AMIS? <Link href="/login" underline="hover">Đăng nhập</Link>
                            </Typography>
                        </Box>

                        <Box component="form" onSubmit={handleSubmit}>
                            {error && (
                                <Alert severity="error" sx={{ mb: 2 }}>
                                    {error}
                                </Alert>
                            )}

                            {/* Product Selection */}
                            <TextField
                                select
                                label="Chọn sản phẩm dùng thử"
                                fullWidth
                                margin="normal"
                                value={formData.productType}
                                onChange={handleSelectChange('productType')}
                                error={!!formErrors.productType}
                                helperText={formErrors.productType}
                                placeholder="Nhấp để chọn"
                            >
                                <MenuItem value="amis-accounting">AMIS Kế toán</MenuItem>
                                <MenuItem value="amis-hrm">AMIS Nhân sự</MenuItem>
                                <MenuItem value="amis-crm">AMIS Khách hàng</MenuItem>
                            </TextField>

                            {/* Business Type */}
                            <Box sx={{ mt: 2, mb: 2 }}>
                                <Typography variant="body2" sx={{ mb: 1 }}>Loại hình kinh doanh</Typography>
                                <RadioGroup
                                    row
                                    value={formData.businessType}
                                    onChange={handleSelectChange('businessType')}
                                >
                                    <FormControlLabel value="doanh-nghiep" control={<Radio />} label="Doanh nghiệp" />
                                    <FormControlLabel value="ho-kinh-doanh" control={<Radio />} label="Hộ kinh doanh" />
                                </RadioGroup>
                            </Box>

                            {/* Company and Tax Code */}
                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <TextField
                                    label="Công ty"
                                    fullWidth
                                    margin="normal"
                                    value={formData.companyName}
                                    onChange={handleInputChange('companyName')}
                                    error={!!formErrors.companyName}
                                    helperText={formErrors.companyName}
                                    placeholder="Tìm theo tên công ty hoặc mã số thuế"
                                    InputProps={{
                                        startAdornment: <BusinessIcon sx={{ mr: 1, color: 'action.active' }} />
                                    }}
                                />
                                <TextField
                                    label="Mã số thuế"
                                    fullWidth
                                    margin="normal"
                                    value={formData.taxCode}
                                    onChange={handleInputChange('taxCode')}
                                    error={!!formErrors.taxCode}
                                    helperText={formErrors.taxCode}
                                    placeholder="Nhập mã số thuế"
                                />
                            </Box>

                            {/* Name Fields */}
                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <TextField
                                    label="Họ và đệm"
                                    fullWidth
                                    margin="normal"
                                    value={formData.firstName}
                                    onChange={handleInputChange('firstName')}
                                    error={!!formErrors.firstName}
                                    helperText={formErrors.firstName}
                                    placeholder="Nhập họ và đệm"
                                    InputProps={{
                                        startAdornment: <PersonIcon sx={{ mr: 1, color: 'action.active' }} />
                                    }}
                                />
                                <TextField
                                    label="Tên"
                                    fullWidth
                                    margin="normal"
                                    value={formData.lastName}
                                    onChange={handleInputChange('lastName')}
                                    error={!!formErrors.lastName}
                                    helperText={formErrors.lastName}
                                    placeholder="Nhập tên của bạn"
                                />
                            </Box>

                            {/* Email and Phone */}
                            <Box sx={{ display: 'flex', gap: 2 }}>
                                <TextField
                                    label="Email"
                                    fullWidth
                                    margin="normal"
                                    type="email"
                                    value={formData.email}
                                    onChange={handleInputChange('email')}
                                    error={!!formErrors.email}
                                    helperText={formErrors.email}
                                    placeholder="Nhập email của bạn"
                                    InputProps={{
                                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'action.active' }} />
                                    }}
                                />
                                <TextField
                                    label="Số điện thoại"
                                    fullWidth
                                    margin="normal"
                                    value={formData.phone}
                                    onChange={handleInputChange('phone')}
                                    error={!!formErrors.phone}
                                    helperText={formErrors.phone}
                                    placeholder="Nhập số điện thoại của bạn"
                                    InputProps={{
                                        startAdornment: <PhoneIcon sx={{ mr: 1, color: 'action.active' }} />
                                    }}
                                />
                            </Box>

                            {/* Position */}
                            <TextField
                                select
                                label="Vị trí công việc"
                                fullWidth
                                margin="normal"
                                value={formData.position}
                                onChange={handleSelectChange('position')}
                                error={!!formErrors.position}
                                helperText={formErrors.position}
                                placeholder="Chọn vị trí công việc của bạn"
                            >
                                <MenuItem value="ceo">Giám đốc điều hành</MenuItem>
                                <MenuItem value="cfo">Giám đốc tài chính</MenuItem>
                                <MenuItem value="accountant">Kế toán</MenuItem>
                                <MenuItem value="hr">Nhân sự</MenuItem>
                                <MenuItem value="manager">Quản lý</MenuItem>
                                <MenuItem value="employee">Nhân viên</MenuItem>
                                <MenuItem value="other">Khác</MenuItem>
                            </TextField>

                            {/* Additional Info Link */}
                            <Box sx={{ mt: 2, mb: 2 }}>
                                <Link href="#" underline="hover" variant="body2">
                                    Thêm mã nhân viên (Dành cho nhân viên MISA đăng ký khách hàng)
                                </Link>
                            </Box>

                            {/* Terms Agreement */}
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={formData.agreeToTerms}
                                        onChange={handleInputChange('agreeToTerms')}
                                        color="primary"
                                    />
                                }
                                label={
                                    <Typography variant="body2">
                                        Tôi đồng ý với các điều khoản của{' '}
                                        <Link href="#" underline="hover">
                                            Chính sách quyền riêng tư
                                        </Link>
                                    </Typography>
                                }
                                sx={{ mt: 1 }}
                            />
                            {formErrors.agreeToTerms && (
                                <Typography variant="caption" color="error" display="block" sx={{ mt: 1 }}>
                                    {formErrors.agreeToTerms}
                                </Typography>
                            )}

                            {/* Submit Button */}
                            <Button
                                type="submit"
                                fullWidth
                                variant="contained"
                                disabled={loading}
                                sx={{
                                    mt: 3,
                                    mb: 2,
                                    py: 1.5,
                                    fontWeight: 600,
                                    backgroundColor: '#9e9e9e',
                                    '&:hover': {
                                        backgroundColor: '#757575'
                                    }
                                }}
                            >
                                {loading ? (
                                    <CircularProgress size={24} color="inherit" />
                                ) : (
                                    'Đăng ký'
                                )}
                            </Button>
                        </Box>
                    </CardContent>
                </Card>
            </Box>
        </Box>
    );
};

export default SignUpScreen;
