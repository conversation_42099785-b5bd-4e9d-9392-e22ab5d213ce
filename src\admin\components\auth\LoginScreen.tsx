import React, { useState } from 'react';

import {
    Analytics,
    BusinessCenter,
    CloudSync,
    Email as EmailIcon,
    Google,
    LockOutlined as LockIcon,
    Lock as PasswordIcon,
    Security,
    SmartToy,
    Visibility,
    VisibilityOff
} from '@mui/icons-material';
import {
    Alert,
    Avatar,
    Box,
    Button,
    Card,
    CardContent,
    Checkbox,
    CircularProgress,
    Container,
    Divider,
    FormControlLabel,
    Grid,
    IconButton,
    InputAdornment,
    Link,
    Paper,
    TextField,
    Typography
} from '@mui/material';
import { useAuth } from '@shared/hooks/useAuth';
import { useNavigate } from '@tanstack/react-router';

const LoginScreen: React.FC = () => {
    const navigate = useNavigate();
    const { login, loading, error } = useAuth();

    const [formData, setFormData] = useState({
        email: '',
        password: '',
        rememberMe: false
    });

    const [showPassword, setShowPassword] = useState(false);
    const [formErrors, setFormErrors] = useState<{
        email?: string;
        password?: string;
    }>({});

    const validateForm = () => {
        const errors: { email?: string; password?: string } = {};

        if (!formData.email) {
            errors.email = 'Email is required';
        } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
            errors.email = 'Email is invalid';
        }

        if (!formData.password) {
            errors.password = 'Password is required';
        } else if (formData.password.length < 6) {
            errors.password = 'Password must be at least 6 characters';
        }

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        const result = await login({
            email: formData.email,
            password: formData.password,
            rememberMe: formData.rememberMe
        });

        if (result.success) {
            navigate({ to: '/dashboard' });
        }
    };

    const handleInputChange =
        (field: keyof typeof formData) => (e: React.ChangeEvent<HTMLInputElement>) => {
            const value = field === 'rememberMe' ? e.target.checked : e.target.value;
            setFormData(prev => ({ ...prev, [field]: value }));

            // Clear field error when user starts typing
            if (formErrors[field as keyof typeof formErrors]) {
                setFormErrors(prev => ({ ...prev, [field]: undefined }));
            }
        };

    return (
        <Box
            sx={{
                minHeight: '100vh',
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                backgroundImage: 'url(/assets/icons/images/windows-11-dark-mode-blue-stock-official-3840x2160-5630.jpg)',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                alignItems: 'center',
                justifyContent: 'center',
                p: 3,
                position: 'relative',
                '&::before': {
                    content: '""',
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.3)',
                    zIndex: 0
                }
            }}
        >
            {/* Combined Cards Container */}
            <Box
                sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    boxShadow: 4,
                    borderRadius: 3,
                    overflow: 'hidden',
                    maxWidth: 840,
                    width: '100%',
                    position: 'relative',
                    zIndex: 1
                }}
            >
                {/* Left Panel - Split Card */}
                <Card
                    sx={{
                        width: { xs: '100%', md: 420 },
                        borderRadius: 0,
                        boxShadow: 'none',
                        overflow: 'hidden'
                    }}
                >
                    {/* Top Half of Left Card */}
                    <Box
                        sx={{
                            height: 250,
                            background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'white',
                            p: 3,
                            position: 'relative',
                            overflow: 'hidden'
                        }}
                    >
                        {/* Background decorative elements */}
                        <Box
                            sx={{
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                opacity: 0.1,
                                background: `
                                    radial-gradient(circle at 20% 20%, rgba(255,255,255,0.3) 0%, transparent 50%),
                                    radial-gradient(circle at 80% 80%, rgba(255,255,255,0.2) 0%, transparent 50%)
                                `
                            }}
                        />

                        <Box sx={{ textAlign: 'center', zIndex: 1 }}>
                            <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 1 }}>
                                HOHO
                            </Typography>
                            <Typography variant="subtitle2" sx={{ opacity: 0.9, mb: 2 }}>
                                hehe
                            </Typography>

                            {/* AI Robot Icon */}
                            <Box
                                sx={{
                                    width: 80,
                                    height: 80,
                                    borderRadius: '50%',
                                    backgroundColor: 'rgba(255,255,255,0.2)',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mx: 'auto',
                                    border: '2px solid rgba(255,255,255,0.3)'
                                }}
                            >
                                <SmartToy sx={{ fontSize: 40, color: 'white' }} />
                            </Box>
                        </Box>
                    </Box>

                    {/* Bottom Half of Left Card */}
                    <CardContent
                        sx={{
                            p: 3,
                            height: 250,
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center'
                        }}
                    >
                        <Typography
                            variant="h6"
                            sx={{ fontWeight: 'bold', mb: 2, textAlign: 'center' }}
                        >
                            Tính năng nổi bật
                        </Typography>

                        {/* Feature Icons */}
                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mb: 3 }}>
                            <Box sx={{ textAlign: 'center' }}>
                                <BusinessCenter sx={{ fontSize: 28, mb: 1, color: '#1976d2' }} />
                                <Typography variant="caption" display="block">
                                    Quản lý tài liệu
                                </Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center' }}>
                                <Analytics sx={{ fontSize: 28, mb: 1, color: '#1976d2' }} />
                                <Typography variant="caption" display="block">
                                    Phân tích tài liệu
                                </Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center' }}>
                                <Security sx={{ fontSize: 28, mb: 1, color: '#1976d2' }} />
                                <Typography variant="caption" display="block">
                                    OCR
                                </Typography>
                            </Box>
                            <Box sx={{ textAlign: 'center' }}>
                                <CloudSync sx={{ fontSize: 28, mb: 1, color: '#1976d2' }} />
                                <Typography variant="caption" display="block">
                                    Đồng bộ dữ liệu
                                </Typography>
                            </Box>
                        </Box>

                        <Typography variant="body2" color="text.secondary" textAlign="center">
                            Giải pháp quản lý toàn diện với công nghệ AI tiên tiến
                        </Typography>
                    </CardContent>
                </Card>

                {/* Right Panel - Login Form */}
                <Card sx={{ width: { xs: '100%', md: 420 }, borderRadius: 0, boxShadow: 'none' }}>
                    <CardContent sx={{ p: 4 }}>
                        <Box sx={{ textAlign: 'center', mb: 3 }}>
                            <img src="/logo.png" alt="logo" height={40} />
                            <Typography variant="h6" sx={{ mt: 2 }}>
                                Đăng nhập
                            </Typography>
                        </Box>

                        <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
                                {error && (
                                    <Alert severity="error" sx={{ mb: 2 }}>
                                        {error}
                                    </Alert>
                                )}
                                <TextField
                                    label="Số điện thoại/ email/ id"
                                    fullWidth
                                    margin="normal"
                                    value={formData.email}
                                    onChange={handleInputChange('email')}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <EmailIcon />
                                            </InputAdornment>
                                        )
                                    }}
                                />
                                <TextField
                                    label="Mật khẩu"
                                    fullWidth
                                    margin="normal"
                                    type={showPassword ? 'text' : 'password'}
                                    value={formData.password}
                                    onChange={handleInputChange('password')}
                                    InputProps={{
                                        startAdornment: (
                                            <InputAdornment position="start">
                                                <PasswordIcon />
                                            </InputAdornment>
                                        ),
                                        endAdornment: (
                                            <InputAdornment position="end">
                                                <IconButton
                                                    onClick={() => setShowPassword(!showPassword)}
                                                >
                                                    {showPassword ? (
                                                        <VisibilityOff />
                                                    ) : (
                                                        <Visibility />
                                                    )}
                                                </IconButton>
                                            </InputAdornment>
                                        )
                                    }}
                                />
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            checked={formData.rememberMe}
                                            onChange={handleInputChange('rememberMe')}
                                        />
                                    }
                                    label="Ghi nhớ đăng nhập"
                                    sx={{ mt: 1 }}
                                />
                                <Button
                                    type="submit"
                                    fullWidth
                                    variant="contained"
                                    color="primary"
                                    disabled={loading}
                                    sx={{ mt: 3, mb: 2, py: 1.5, fontWeight: 600 }}
                                >
                                    {loading ? (
                                        <CircularProgress size={24} color="inherit" />
                                    ) : (
                                        'Đăng nhập'
                                    )}
                                </Button>

                                <Box
                                    sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}
                                >
                                    <Link href="#" underline="hover">
                                        Quên mật khẩu?
                                    </Link>
                                    <Link
                                        component="button"
                                        type="button"
                                        underline="hover"
                                        onClick={() => navigate({ to: '/signup' })}
                                        sx={{ cursor: 'pointer' }}
                                    >
                                        Đăng ký
                                    </Link>
                                </Box>
                                <Box sx={{ mt: 3, textAlign: 'center' }}>
                                    <Typography variant="body2" color="text.secondary">
                                        Demo Credentials:
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        Admin: <EMAIL> / admin123
                                    </Typography>
                                    <Typography variant="body2" color="text.secondary">
                                        User: <EMAIL> / user123
                                    </Typography>
                                </Box>
                            </Box>

                        <Divider sx={{ my: 3 }}>Hoặc đăng nhập với</Divider>

                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2 }}>
                            <Google sx={{ fontSize: 28, mb: 1, color: '#bc0ddfff' }} />
                            <Google sx={{ fontSize: 28, mb: 1, color: '#09c768ff' }} />
                            <Google sx={{ fontSize: 28, mb: 1, color: '#e2250cff' }} />
                        </Box>
                    </CardContent>
                </Card>
            </Box>
        </Box>
    );
};

export default LoginScreen;
